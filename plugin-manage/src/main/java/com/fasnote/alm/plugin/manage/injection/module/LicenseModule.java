package com.fasnote.alm.plugin.manage.injection.module;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasnote.alm.injection.api.IBinder;
import com.fasnote.alm.injection.api.IInjectionContext;
import com.fasnote.alm.injection.api.IModule;
import com.fasnote.alm.injection.api.IServiceProvider;
import com.fasnote.alm.plugin.manage.api.IBundleManager;
import com.fasnote.alm.plugin.manage.api.IClassLoaderManager;
import com.fasnote.alm.plugin.manage.api.ILicenseFileManager;
import com.fasnote.alm.plugin.manage.api.ILicenseManager;
import com.fasnote.alm.plugin.manage.api.ILicenseValidator;
import com.fasnote.alm.plugin.manage.api.IPluginRuntimeCoordinator;
import com.fasnote.alm.plugin.manage.api.IRuntimeEnvironmentManager;
import com.fasnote.alm.plugin.manage.api.IServiceRegistrationManager;
import com.fasnote.alm.plugin.manage.api.LicenseAware;
import com.fasnote.alm.plugin.manage.core.BundleManager;
import com.fasnote.alm.plugin.manage.core.ClassLoaderManager;
import com.fasnote.alm.plugin.manage.core.LicenseFileManager;
import com.fasnote.alm.plugin.manage.core.LicenseManager;
import com.fasnote.alm.plugin.manage.core.LicenseValidator;
import com.fasnote.alm.plugin.manage.core.PluginRuntimeCoordinator;
import com.fasnote.alm.plugin.manage.core.RuntimeEnvironmentManager;
import com.fasnote.alm.plugin.manage.core.ServiceRegistrationManager;
import com.fasnote.alm.plugin.manage.core.UnifiedLicenseProcessor;
import com.fasnote.alm.plugin.manage.monitor.FrameworkMonitor;
import com.fasnote.alm.plugin.manage.config.LicenseConfiguration;
import com.fasnote.alm.plugin.manage.security.SecurityValidator;
import com.fasnote.alm.plugin.manage.security.MachineCodeProvider;
import com.fasnote.alm.plugin.manage.security.PolarionMachineCodeProvider;
import com.fasnote.alm.plugin.manage.repository.impl.InMemoryLicenseRepository;

import com.fasnote.alm.plugin.manage.injection.resolver.LicenseAwareServiceResolver;
import com.fasnote.alm.plugin.manage.model.PluginLicense;
import com.fasnote.alm.plugin.manage.registry.LicenseServiceRegistry;
import com.fasnote.alm.injection.api.IPostProcessor;
import com.fasnote.alm.injection.api.IDependencyInjector;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 统一许可证模块
 *
 * 整合了 LicenseManager 的依赖注入配置和许可证感知服务功能
 *
 * 核心功能：
 * 1. 配置 LicenseManager 的依赖注入（单例模式）
 * 2. 注册许可证感知服务拦截器
 * 3. 根据许可证状态选择 LicenseImplementation 或 FallbackImplementation
 * 4. 支持许可证动态更新时的服务切换
 */
public class LicenseModule implements IModule {


	// 移除了复杂的ServiceRegistrationManager Provider类
	// 现在使用自动依赖注入

	/**
	 * 许可证感知服务提供者
	 */
	private class LicenseAwareProvider implements IServiceProvider<LicenseAware> {

		@Override
		public LicenseAware provide(IInjectionContext context) {
			// 返回一个简单的许可证信息访问器
			return new LicenseAware() {
				private PluginLicense license;

				@Override
				public PluginLicense getLicenseInfo() {
					return license;
				}

				@Override
				public void setLicenseInfo(PluginLicense license) {
					this.license = license;
				}
			};
		}
	}

	private static final Logger logger = LoggerFactory.getLogger(LicenseModule.class);

	/**
	 * 无参构造函数（用于依赖注入框架自动扫描）
	 *
	 * 模块注册的设计原则：
	 * 1. 模块应该是配置单元，不应该依赖外部状态
	 * 2. 模块的依赖应该通过 configure() 方法中的依赖查找获取
	 * 3. 这样可以确保模块的可重用性和测试性
	 */
	public LicenseModule() {
		logger.info("LicenseModule使用无参构造函数创建");
	}

	@Override
	public void configure(IBinder binder) {
		logger.info("配置统一许可证模块（重构后）...");

		// 2. 注册基础组件
		registerCoreComponents(binder);

		// 3. 注册所有管理器组件
		registerManagerComponents(binder);

		// 5. 注册 LicenseManager 和 ILicenseManager 接口绑定
		binder.bind(LicenseManager.class).asSingleton().build();
		binder.bind(ILicenseManager.class, LicenseManager.class).asSingleton().build();
		logger.info("LicenseManager依赖注入配置完成");

		// LicenseAwareServiceResolver 现在使用 @Service 注解，会被自动扫描和注册
		// 但是需要手动注册为服务解析器
		binder.registerPostProcessor(new IPostProcessor() {
			@Override
			public void postProcessAfterSingletonCreation(IDependencyInjector injector) {
				// 在所有单例创建完成后注册服务解析器
				LicenseAwareServiceResolver resolver = injector.getService(LicenseAwareServiceResolver.class);
				if (resolver != null) {
					injector.registerServiceResolver(resolver);
					logger.info("在单例创建完成后注册许可证感知服务解析器");
				}
			}

			@Override
			public int getPriority() {
				return 0; // 最高优先级
			}
		});

		// 7. 注册许可证感知服务提供者
		binder.bind(LicenseAware.class).toProvider(new LicenseAwareProvider()).build();

		// 8. 注册FrameworkMonitor（使用自动依赖注入）
		binder.bind(FrameworkMonitor.class).asSingleton().build();

		logger.info("统一许可证模块配置完成（重构后）");
	}

	/**
	 * 注册核心组件（可选，用于需要直接访问特定管理器的场景）
	 */
	private void registerCoreComponents(IBinder binder) {
		logger.debug("注册核心组件...");

		// 注册共享的pluginLicenses Map
		binder.bind(Map.class).toProvider(context -> new ConcurrentHashMap<String, PluginLicense>())
			.named("pluginLicenses").asSingleton().build();

		// 注册统一许可证处理器
		binder.bind(UnifiedLicenseProcessor.class).asSingleton().build();

		// 注册许可证服务注册表
		binder.bind(LicenseServiceRegistry.class).asSingleton().build();

		// 注册MachineCodeProvider
		binder.bind(MachineCodeProvider.class, PolarionMachineCodeProvider.class).asSingleton().build();

		// 注册SecurityValidator
		binder.bind(SecurityValidator.class).asSingleton().build();

		// 注册InMemoryLicenseRepository
		binder.bind(InMemoryLicenseRepository.class).asSingleton().build();

		// 注册LicenseConfiguration单例
		binder.bind(LicenseConfiguration.class, LicenseConfiguration.getInstance());

		logger.debug("核心组件注册完成");
	}

	/**
	 * 注册所有管理器组件
	 */
	private void registerManagerComponents(IBinder binder) {
		logger.debug("注册管理器组件...");

		// 注册BundleManager（使用自动依赖注入）
		binder.bind(BundleManager.class).asSingleton().build();
		binder.bind(IBundleManager.class, BundleManager.class).asSingleton().build();

		// 注册ClassLoaderManager（使用自动依赖注入）
		binder.bind(ClassLoaderManager.class).asSingleton().build();
		binder.bind(IClassLoaderManager.class, ClassLoaderManager.class).asSingleton().build();

		// 注册LicenseValidator（使用自动依赖注入）
		binder.bind(LicenseValidator.class).asSingleton().build();
		binder.bind(ILicenseValidator.class, LicenseValidator.class).asSingleton().build();

		// 注册ServiceRegistrationManager（使用自动依赖注入）
		binder.bind(ServiceRegistrationManager.class).asSingleton().build();
		binder.bind(IServiceRegistrationManager.class, ServiceRegistrationManager.class).asSingleton().build();

		// 注册PluginRuntimeCoordinator（使用自动依赖注入）
		binder.bind(PluginRuntimeCoordinator.class).asSingleton().build();
		binder.bind(IPluginRuntimeCoordinator.class, PluginRuntimeCoordinator.class).asSingleton().build();

		// 注册RuntimeEnvironmentManager（使用自动依赖注入）
		binder.bind(RuntimeEnvironmentManager.class).asSingleton().build();
		binder.bind(IRuntimeEnvironmentManager.class, RuntimeEnvironmentManager.class).asSingleton().build();

		// 注册LicenseFileManager（使用自动依赖注入）
		binder.bind(LicenseFileManager.class).asSingleton().build();
		binder.bind(ILicenseFileManager.class, LicenseFileManager.class).asSingleton().build();

		logger.debug("管理器组件注册完成");
	}



	/**
	 * 清理实现类缓存（支持许可证动态更新）
	 *
	 * @param serviceInterface 要清理的服务接口，如果为null则清理所有缓存
	 */
	public void clearImplementationCache(Class<?> serviceInterface) {
		// 由于服务解析器现在是独立创建的，这个方法暂时不实现
		logger.info("许可证实现缓存清理请求: {}", serviceInterface != null ? serviceInterface.getName() : "全部");
	}

	@Override
	public String getName() {
		return "RefactoredLicenseModule";
	}

	@Override
	public int getPriority() {
		return 5; // 更高优先级，确保重构后的 LicenseManager 在其他许可证相关模块之前初始化
	}









}
package com.fasnote.alm.injection.impl;

import java.lang.annotation.Annotation;
import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

import org.osgi.framework.Bundle;
import org.osgi.framework.BundleContext;
import org.osgi.framework.FrameworkUtil;
import org.osgi.framework.wiring.BundleWiring;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.inject.Inject;
import javax.inject.Singleton;
import javax.annotation.PostConstruct;
import javax.inject.Named;

import com.fasnote.alm.injection.annotations.Qualifier;
import com.fasnote.alm.injection.annotations.Service;
import com.fasnote.alm.injection.utils.AliasForUtils;
import com.fasnote.alm.injection.aop.MethodInterceptorProxy;
import com.fasnote.alm.injection.api.IDependencyInjector;
import com.fasnote.alm.injection.api.IEnhancedServiceInterceptor;
import com.fasnote.alm.injection.api.IInjectionContext;
import com.fasnote.alm.injection.api.ILicenseValidator;
import com.fasnote.alm.injection.api.IModule;
import com.fasnote.alm.injection.api.IPostProcessor;
import com.fasnote.alm.injection.api.IServiceInterceptor;
import com.fasnote.alm.injection.api.IServiceProvider;
import com.fasnote.alm.injection.api.IServiceResolver;
import com.fasnote.alm.injection.api.IServiceValidationInterceptor;
import com.fasnote.alm.injection.api.InterceptContext;

/**
 * 纯粹的依赖注入器实现
 * 专注于依赖注入功能，不包含许可证相关逻辑
 * 支持OSGi环境但不依赖加密类加载器
 */
public class DependencyInjector implements IDependencyInjector {
    
    private static final Logger logger = LoggerFactory.getLogger(DependencyInjector.class);
    
    // 服务注册表
    private final Map<Class<?>, Object> singletonServices = new ConcurrentHashMap<>();
    private final Map<Class<?>, Class<?>> serviceImplementations = new ConcurrentHashMap<>();
    private final Map<String, Object> namedServices = new ConcurrentHashMap<>();

    // 延迟单例映射
    private final Set<Class<?>> lazySingletonServices = ConcurrentHashMap.newKeySet();
    
    // 多实现支持
    private final Map<Class<?>, Map<String, Class<?>>> multipleImplementations = new ConcurrentHashMap<>();
    private final Map<Class<?>, String> primaryImplementations = new ConcurrentHashMap<>();
    private final Map<Class<?>, List<Class<?>>> implementationsByPriority = new ConcurrentHashMap<>();
    
    // OSGi服务注册表
    private final Map<Class<?>, Object> osgiServices = new ConcurrentHashMap<>();
    
    // 模块支持
    private final List<IModule> installedModules = new ArrayList<>();
    private final List<IServiceInterceptor> interceptors = new ArrayList<>();
    private final List<IEnhancedServiceInterceptor> enhancedInterceptors = new ArrayList<>();
    private final Map<Class<?>, IServiceProvider<?>> providers = new ConcurrentHashMap<>();

    // 许可证验证支持
    private final List<IServiceValidationInterceptor> validationInterceptors = new ArrayList<>();

    // 服务解析器支持
    private final List<IServiceResolver> serviceResolvers = new ArrayList<>();

    // 后置处理器支持
    private final List<IPostProcessor> postProcessors = new ArrayList<>();

    // OSGi相关组件
    private BundleContext bundleContext;
    private Bundle hostBundle;
    private final Map<String, Bundle> bundleRegistry = new ConcurrentHashMap<>();

    // Bundle映射表（用于准确的插件ID识别）
    private final Map<Class<?>, Bundle> classToBundleMap = new ConcurrentHashMap<>();
    private final Map<String, Bundle> serviceNameToBundleMap = new ConcurrentHashMap<>();

    // 包扫描提供者服务跟踪
    private org.osgi.util.tracker.ServiceTracker<com.fasnote.alm.injection.api.IPackageScanProvider, com.fasnote.alm.injection.api.IPackageScanProvider> packageScanProviderTracker;
    private final java.util.Map<String, String[]> bundlePackageMappings = new java.util.concurrent.ConcurrentHashMap<>();
    private final java.util.Set<String> scannedBundles = java.util.concurrent.ConcurrentHashMap.newKeySet();

    // 正在创建的对象（用于循环依赖检测）
    private final ThreadLocal<Set<Class<?>>> creatingObjects = ThreadLocal.withInitial(HashSet::new);
    
    /**
     * 无参构造函数（用于运行时模式）
     */
    public DependencyInjector() {
        logger.info("纯粹依赖注入器已初始化（非OSGi模式）");
    }
    
    /**
     * OSGi感知的构造函数
     *
     * @param bundleContext OSGi Bundle上下文
     */
    public DependencyInjector(BundleContext bundleContext) {
        this.bundleContext = bundleContext;
        this.hostBundle = bundleContext != null ? bundleContext.getBundle() : null;

        logger.info("创建OSGi感知的纯粹依赖注入器");

        // 注册当前bundle
        if (hostBundle != null) {
            bundleRegistry.put(hostBundle.getSymbolicName(), hostBundle);
        }

        // 初始化包扫描提供者跟踪机制
        if (bundleContext != null) {
            initializePackageScanProviderTracking();
        }

        // 自动注册 OSGi 服务到依赖注入容器
        registerOSGiServices();
    }
    
    /**
     * 注册Bundle到注册表
     *
     * @param bundle OSGi Bundle
     */
    @Override
    public void registerBundle(Bundle bundle) {
        if (bundle != null) {
            bundleRegistry.put(bundle.getSymbolicName(), bundle);
            logger.debug("注册Bundle: {}", bundle.getSymbolicName());
        }
    }

    /**
     * 从注册表中移除Bundle
     *
     * @param bundle OSGi Bundle
     */
    @Override
    public void unregisterBundle(Bundle bundle) {
        if (bundle != null) {
            Bundle removed = bundleRegistry.remove(bundle.getSymbolicName());
            if (removed != null) {
                logger.debug("注销Bundle: {}", bundle.getSymbolicName());
            } else {
                logger.debug("Bundle未注册，无需注销: {}", bundle.getSymbolicName());
            }
        }
    }

    @Override
    public <T> void registerSingleton(Class<T> serviceClass, T serviceInstance) {
        if (serviceClass == null || serviceInstance == null) {
            throw new IllegalArgumentException("服务类和实例不能为空");
        }

        // 检测并记录Bundle映射关系
        Class<?> instanceClass = serviceInstance.getClass();
        Bundle bundle = detectBundleForClass(instanceClass);
        if (bundle != null) {
            // 记录服务类和实例类到Bundle的映射
            recordClassToBundleMapping(serviceClass, bundle);
            recordClassToBundleMapping(instanceClass, bundle);

            logger.debug("记录Bundle映射 - 单例服务: {}, 实例类: {}, Bundle: {}",
                        serviceClass.getName(), instanceClass.getName(), bundle.getSymbolicName());
        } else {
            logger.debug("无法检测Bundle - 单例服务: {}, 实例类: {}",
                        serviceClass.getName(), instanceClass.getName());
        }

        singletonServices.put(serviceClass, serviceInstance);
        logger.info("注册单例服务: {} -> {}, 实例类型: {}",
                   serviceClass.getName(),
                   serviceInstance.getClass().getName(),
                   serviceInstance.getClass().getSimpleName());
    }

    @Override
    public <T> void registerSingleton(Class<T> serviceClass, T serviceInstance, String name) {
        if (serviceClass == null || serviceInstance == null) {
            throw new IllegalArgumentException("服务类和实例不能为空");
        }

        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("服务名称不能为空");
        }

        // 检测并记录Bundle映射关系
        Class<?> instanceClass = serviceInstance.getClass();
        Bundle bundle = detectBundleForClass(instanceClass);
        if (bundle != null) {
            // 记录服务类和实例类到Bundle的映射
            recordClassToBundleMapping(serviceClass, bundle);
            recordClassToBundleMapping(instanceClass, bundle);

            logger.debug("记录Bundle映射 - 命名单例服务: {}, 实例类: {}, Bundle: {}, 名称: {}",
                        serviceClass.getName(), instanceClass.getName(), bundle.getSymbolicName(), name);
        } else {
            logger.debug("无法检测Bundle - 命名单例服务: {}, 实例类: {}, 名称: {}",
                        serviceClass.getName(), instanceClass.getName(), name);
        }

        // 注册命名服务
        String serviceKey = serviceClass.getName() + "#" + name;
        namedServices.put(serviceKey, serviceInstance);

        // 同时注册到多实现映射中
        multipleImplementations.computeIfAbsent(serviceClass, k -> new ConcurrentHashMap<>())
                              .put(name, serviceInstance.getClass());

        logger.debug("注册命名单例服务: {} -> {} (name={})", serviceClass.getName(), serviceInstance.getClass().getName(), name);
    }

    @Override
    public Object getServiceByServiceName(String serviceName) {
        if (serviceName == null || serviceName.trim().isEmpty()) {
            logger.warn("服务名称不能为空");
            return null;
        }

        logger.debug("通过服务名称查找服务: {}", serviceName);

        // 在所有命名服务中查找匹配的服务名称
        for (Map.Entry<String, Object> entry : namedServices.entrySet()) {
            String serviceKey = entry.getKey();
            // serviceKey 格式为 "interfaceName#serviceName"
            if (serviceKey.endsWith("#" + serviceName)) {
                Object service = entry.getValue();
                logger.debug("找到匹配的命名服务: {} -> {}", serviceKey, service.getClass().getName());
                return service;
            }
        }

        logger.debug("未找到匹配的服务名称: {}", serviceName);
        return null;
    }

    @Override
    public <T> void registerImplementation(Class<T> serviceClass, Class<? extends T> implementationClass) {
        registerImplementation(serviceClass, implementationClass, null, false);
    }
    
    @Override
    public <T> void registerImplementation(Class<T> serviceClass, Class<? extends T> implementationClass,
                                         String name, boolean primary) {
        if (serviceClass == null || implementationClass == null) {
            throw new IllegalArgumentException("服务类和实现类不能为空");
        }

        // 1. 检测并记录Bundle映射关系
        Bundle bundle = detectBundleForClass(implementationClass);
        if (bundle != null) {
            // 记录服务类和实现类到Bundle的映射
            recordClassToBundleMapping(serviceClass, bundle);
            recordClassToBundleMapping(implementationClass, bundle);

            // 记录服务名称到Bundle的映射
            if (name != null) {
                recordServiceNameToBundleMapping(name, bundle);
            }

            logger.debug("记录Bundle映射 - 服务: {}, 实现: {}, 名称: {}, Bundle: {}",
                        serviceClass.getName(), implementationClass.getName(), name, bundle.getSymbolicName());
        } else {
            logger.debug("无法检测Bundle - 服务: {}, 实现: {}, 名称: {}",
                        serviceClass.getName(), implementationClass.getName(), name);
        }

        // 2. 注册到多实现映射
        multipleImplementations.computeIfAbsent(serviceClass, k -> new ConcurrentHashMap<>())
                              .put(name != null ? name : implementationClass.getSimpleName(), implementationClass);
        
        // 2. 如果是主要实现，记录
        if (primary) {
            primaryImplementations.put(serviceClass, name != null ? name : implementationClass.getSimpleName());
            // 主要实现也注册到单一实现映射中（向后兼容）
            serviceImplementations.put(serviceClass, implementationClass);
        }
        
        // 3. 按优先级排序
        implementationsByPriority.computeIfAbsent(serviceClass, k -> new ArrayList<>()).add(implementationClass);
        implementationsByPriority.get(serviceClass).sort((a, b) -> {
            int priorityA = getImplementationPriority(a);
            int priorityB = getImplementationPriority(b);
            return Integer.compare(priorityA, priorityB); // 数值小的优先级高
        });
        
        // 4. 如果没有设置主要实现，使用优先级最高的
        if (!primary && !primaryImplementations.containsKey(serviceClass)) {
            List<Class<?>> impls = implementationsByPriority.get(serviceClass);
            if (!impls.isEmpty()) {
                Class<?> highestPriorityImpl = impls.get(0);
                serviceImplementations.put(serviceClass, highestPriorityImpl);
            }
        }
        
        logger.debug("注册服务实现: {} -> {} {}{}", 
                   serviceClass.getName(), 
                   implementationClass.getName(),
                   name != null ? " (name=" + name + ")" : "",
                   primary ? " [PRIMARY]" : "");
    }

    @Override
    public <T> void registerSingletonImplementation(Class<T> serviceClass, Class<? extends T> implementationClass,
                                                   String name, boolean primary) {
        if (serviceClass == null || implementationClass == null) {
            throw new IllegalArgumentException("服务类和实现类不能为空");
        }

        // 1. 检测并记录Bundle映射关系
        Bundle bundle = detectBundleForClass(implementationClass);
        if (bundle != null) {
            // 记录服务类和实现类到Bundle的映射
            recordClassToBundleMapping(serviceClass, bundle);
            recordClassToBundleMapping(implementationClass, bundle);

            // 记录服务名称到Bundle的映射
            if (name != null) {
                recordServiceNameToBundleMapping(name, bundle);
            }

            logger.debug("记录Bundle映射 - 单例服务: {}, 实现: {}, 名称: {}, Bundle: {}",
                        serviceClass.getName(), implementationClass.getName(), name, bundle.getSymbolicName());
        } else {
            logger.debug("无法检测Bundle - 单例服务: {}, 实现: {}, 名称: {}",
                        serviceClass.getName(), implementationClass.getName(), name);
        }

        // 2. 注册到多实现映射（延迟初始化的单例）
        multipleImplementations.computeIfAbsent(serviceClass, k -> new ConcurrentHashMap<>())
                              .put(name != null ? name : implementationClass.getSimpleName(), implementationClass);

        // 3. 标记为延迟单例
        lazySingletonServices.add(serviceClass);

        // 4. 如果是主要实现，记录
        if (primary) {
            primaryImplementations.put(serviceClass, name != null ? name : implementationClass.getSimpleName());
            // 主要实现也注册到单一实现映射中（延迟初始化）
            serviceImplementations.put(serviceClass, implementationClass);
        }

        // 4. 按优先级排序
        implementationsByPriority.computeIfAbsent(serviceClass, k -> new ArrayList<>()).add(implementationClass);
        implementationsByPriority.get(serviceClass).sort((a, b) -> {
            int priorityA = getImplementationPriority(a);
            int priorityB = getImplementationPriority(b);
            return Integer.compare(priorityA, priorityB); // 数值小的优先级高
        });

        // 5. 如果没有设置主要实现，使用优先级最高的
        if (!primary && !primaryImplementations.containsKey(serviceClass)) {
            List<Class<?>> impls = implementationsByPriority.get(serviceClass);
            if (!impls.isEmpty()) {
                Class<?> highestPriorityImpl = impls.get(0);
                serviceImplementations.put(serviceClass, highestPriorityImpl);
            }
        }

        logger.debug("注册单例服务实现: {} -> {} {}{} [LAZY_SINGLETON]",
                   serviceClass.getName(),
                   implementationClass.getName(),
                   name != null ? " (name=" + name + ")" : "",
                   primary ? " [PRIMARY]" : "");
    }

    /**
     * 获取实现类的优先级
     */
    private int getImplementationPriority(Class<?> implementationClass) {
        Qualifier qualifier = implementationClass.getAnnotation(Qualifier.class);
        return qualifier != null ? qualifier.priority() : 0;
    }

    @Override
    public <T> T getService(Class<T> serviceClass) {
        try {
            Object service = resolveService(serviceClass, null);
            T typedService = serviceClass.cast(service);

            // 应用服务验证拦截器
            return applyValidationInterceptors(serviceClass, null, typedService);

        } catch (Exception e) {
            logger.error("获取服务失败: {}", serviceClass.getName(), e);
            return null;
        }
    }

    @Override
    public <T> T getService(Class<T> serviceClass, String name) {
        try {
            Object service = resolveServiceByName(serviceClass, name);
            T typedService = serviceClass.cast(service);

            // 应用服务验证拦截器
            return applyValidationInterceptors(serviceClass, name, typedService);

        } catch (Exception e) {
            logger.error("获取命名服务失败: {} (name={})", serviceClass.getName(), name, e);
            return null;
        }
    }

    @Override
    public Object getServiceByName(String interfaceName) {
        try {
            return resolveServiceByInterfaceName(interfaceName, null);
        } catch (Exception e) {
            logger.error("基于接口名称获取服务失败: {}", interfaceName, e);
            return null;
        }
    }

    @Override
    public Object getServiceByName(String interfaceName, String serviceName) {
        try {
            return resolveServiceByInterfaceName(interfaceName, serviceName);
        } catch (Exception e) {
            logger.error("基于接口名称获取命名服务失败: {} (name={})", interfaceName, serviceName, e);
            return null;
        }
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T> List<T> getAllImplementations(Class<T> serviceClass) {
        List<T> implementations = new ArrayList<>();
        
        try {
            Map<String, Class<?>> impls = multipleImplementations.get(serviceClass);
            if (impls != null) {
                for (Class<?> implClass : impls.values()) {
                    Object instance = createInstance(implClass);
                    implementations.add((T) instance);
                }
            }
        } catch (Exception e) {
            logger.error("获取所有实现失败: {}", serviceClass.getName(), e);
        }
        
        return implementations;
    }

    @Override
    public <T> T createInstance(Class<T> clazz) throws Exception {
        logger.debug("创建实例: {}", clazz.getName());

        // 检查循环依赖
        Set<Class<?>> creating = creatingObjects.get();
        if (creating.contains(clazz)) {
            throw new RuntimeException("检测到循环依赖: " + clazz.getName());
        }

        try {
            creating.add(clazz);

            // 创建注入上下文
            InjectionContext context = new InjectionContext(this, clazz, null);

            // 应用前置拦截器
            Object interceptedInstance = applyBeforeInterceptors(clazz, context);
            if (interceptedInstance != null) {
                return clazz.cast(interceptedInstance);
            }
            
            // 检查是否为单例
            Object singleton = singletonServices.get(clazz);
            if (singleton != null) {
                logger.debug("返回单例实例: {}", clazz.getName());
                return clazz.cast(singleton);
            }
            
            // 检查是否有自定义提供者
            @SuppressWarnings("unchecked")
            IServiceProvider<T> provider = (IServiceProvider<T>) providers.get(clazz);
            if (provider != null) {
                T instance = provider.provide(context);
                if (instance != null) {
                    logger.debug("通过提供者创建实例: {}", clazz.getName());
                    return clazz.cast(applyAfterInterceptors(clazz, instance, context));
                }
            }

            // 创建新实例
            T instance = createNewInstance(clazz);

            // 应用前置后置处理器
            instance = clazz.cast(applyPostProcessorsBeforeInjection(instance, clazz));

            // 注入依赖
            injectDependencies(instance);

            // 应用后置后置处理器
            instance = clazz.cast(applyPostProcessorsAfterInjection(instance, clazz));

            // 调用初始化方法
            callInitMethods(instance);

            // 应用后置拦截器
            instance = clazz.cast(applyAfterInterceptors(clazz, instance, context));
            
            logger.debug("成功创建并注入依赖: {}", clazz.getName());
            return instance;
            
        } finally {
            creating.remove(clazz);
        }
    }

    @Override
    public void installModule(IModule module) {
        if (module == null) {
            throw new IllegalArgumentException("模块不能为空");
        }

        // 检查模块是否已安装（避免重复安装）
        if (installedModules.stream().anyMatch(m -> m.getClass().equals(module.getClass()))) {
            logger.debug("模块已安装，跳过: {}", module.getName());
            return;
        }

        // 检查并安装依赖模块
        installModuleDependencies(module);

        installedModules.add(module);

        // 按优先级排序
        installedModules.sort((a, b) -> Integer.compare(a.getPriority(), b.getPriority()));

        // 配置模块
        Binder binder = new Binder(this);
        module.configure(binder);

        logger.info("已安装模块: {} (优先级: {})", module.getName(), module.getPriority());

        // 执行组件扫描（类似Spring的@ComponentScan）
        performComponentScan(module);

        // 触发后置处理器的单例创建完成回调
        triggerPostProcessorsAfterSingletonCreation();
    }

    /**
     * 执行组件扫描
     * 类似于Spring的@ComponentScan功能
     */
    private void performComponentScan(IModule module) {
        if (!module.isComponentScanEnabled()) {
            return;
        }

        List<String> scanPackages = module.getComponentScanPackages();
        if (scanPackages == null || scanPackages.isEmpty()) {
            return;
        }

        logger.info("执行组件扫描 - 模块: {}, 扫描包: {}", module.getName(), scanPackages);

        for (String packageName : scanPackages) {
            try {
                logger.debug("扫描组件包: {}", packageName);
                scanAndInstallModules(packageName);
            } catch (Exception e) {
                logger.warn("扫描组件包失败: {} - {}", packageName, e.getMessage());
            }
        }
    }

    @Override
    public void installModules(IModule... modules) {
        for (IModule module : modules) {
            installModule(module);
        }
    }

    @Override
    public void scanAndInstallModules(String packageName) {
        logger.debug("扫描模块包: {}", packageName);

        try {
            // 获取类路径下的所有类
            List<Class<?>> classes = scanClasses(packageName);

            for (Class<?> clazz : classes) {
                if (IModule.class.isAssignableFrom(clazz) &&
                    !clazz.isInterface() &&
                    !java.lang.reflect.Modifier.isAbstract(clazz.getModifiers())) {

                    try {
                        // 使用统一的模块创建逻辑
                        IModule module = createModuleInstance(clazz);
                        if (module != null) {
                            installModule(module);
                        } else {
                            logger.error("创建模块实例失败: {}", clazz.getName());
                        }
                    } catch (Exception e) {
                        logger.error("创建模块实例失败: {}", clazz.getName(), e);
                    }
                }
            }
        } catch (Exception e) {
            logger.error("扫描模块失败: {}", packageName, e);
        }
    }

    @Override
    public List<IModule> getInstalledModules() {
        return new ArrayList<>(installedModules);
    }

    @Override
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new ConcurrentHashMap<>();
        stats.put("singletonServices", singletonServices.size());
        stats.put("serviceImplementations", serviceImplementations.size());
        stats.put("namedServices", namedServices.size());
        stats.put("osgiServices", osgiServices.size());
        stats.put("totalServices", getRegisteredServiceCount());
        
        return stats;
    }

    @Override
    public void clear() {
        logger.info("清理依赖注入器");
        singletonServices.clear();
        serviceImplementations.clear();
        namedServices.clear();
        osgiServices.clear();
        installedModules.clear();
        interceptors.clear();
        providers.clear();
    }
    
    /**
     * 获取已注册的服务数量
     */
    public int getRegisteredServiceCount() {
        return singletonServices.size() + serviceImplementations.size() + namedServices.size() + osgiServices.size();
    }
    
    /**
     * 注册拦截器（内部使用）
     */
    public void registerInterceptor(IServiceInterceptor interceptor) {
        if (interceptor != null) {
            interceptors.add(interceptor);
            // 按优先级排序
            interceptors.sort((a, b) -> Integer.compare(a.getPriority(), b.getPriority()));
            logger.debug("注册拦截器: {} (优先级: {})", interceptor.getClass().getSimpleName(), interceptor.getPriority());

            // 如果是增强拦截器，同时注册到增强拦截器列表
            if (interceptor instanceof IEnhancedServiceInterceptor) {
                enhancedInterceptors.add((IEnhancedServiceInterceptor) interceptor);
                enhancedInterceptors.sort((a, b) -> Integer.compare(a.getPriority(), b.getPriority()));
                logger.debug("同时注册为增强拦截器: {}", interceptor.getClass().getSimpleName());
            }
        }
    }
    
    /**
     * 注册许可证验证器
     *
     * @param validator 许可证验证器
     */
    public void registerLicenseValidator(ILicenseValidator validator) {
        if (validator != null) {
            this.licenseValidator = validator;
            logger.debug("注册许可证验证器: {}", validator.getClass().getSimpleName());
        }
    }

    /**
     * 注册服务验证拦截器
     *
     * @param interceptor 服务验证拦截器
     */
    public void registerValidationInterceptor(IServiceValidationInterceptor interceptor) {
        if (interceptor != null) {
            validationInterceptors.add(interceptor);
            // 按优先级排序
            validationInterceptors.sort((a, b) -> Integer.compare(a.getPriority(), b.getPriority()));
            logger.debug("注册服务验证拦截器: {} (优先级: {})",
                       interceptor.getClass().getSimpleName(), interceptor.getPriority());
        }
    }

    /**
     * 注册服务解析器
     *
     * @param resolver 服务解析器
     */
    @Override
    public void registerServiceResolver(IServiceResolver resolver) {
        if (resolver != null) {
            serviceResolvers.add(resolver);
            // 按优先级排序
            serviceResolvers.sort((a, b) -> Integer.compare(a.getPriority(), b.getPriority()));
            logger.debug("注册服务解析器: {} (优先级: {})",
                       resolver.getClass().getSimpleName(), resolver.getPriority());
        }
    }

    /**
     * 注册后置处理器
     *
     * @param postProcessor 后置处理器
     */
    @Override
    public void registerPostProcessor(IPostProcessor postProcessor) {
        if (postProcessor != null) {
            postProcessors.add(postProcessor);
            // 按优先级排序
            postProcessors.sort((a, b) -> Integer.compare(a.getPriority(), b.getPriority()));
            logger.debug("注册后置处理器: {} (优先级: {})",
                        postProcessor.getName(), postProcessor.getPriority());
        }
    }

    /**
     * 注册提供者（内部使用）
     */
    @SuppressWarnings("unchecked")
    public <T> void registerProvider(Class<T> serviceClass, IServiceProvider<T> provider) {
        if (serviceClass != null && provider != null) {
            providers.put(serviceClass, provider);
            logger.debug("注册提供者: {}", serviceClass.getName());
        }
    }

    /**
     * 应用服务验证拦截器
     *
     * @param serviceClass 服务接口类
     * @param serviceName 服务名称
     * @param normalService 正常的服务实例
     * @return 经过拦截器处理后的服务实例
     */
    private <T> T applyValidationInterceptors(Class<T> serviceClass, String serviceName, T normalService) {
        if (validationInterceptors.isEmpty() || normalService == null) {
            return normalService;
        }

        T currentService = normalService;

        for (IServiceValidationInterceptor interceptor : validationInterceptors) {
            try {
                if (interceptor.shouldIntercept(serviceClass, serviceName)) {
                    // 从服务名称推断插件ID
                    String pluginId = extractPluginIdFromServiceName(serviceName, serviceClass);
                    InterceptContext context = new InterceptContext(pluginId);

                    currentService = interceptor.interceptServiceRequest(serviceClass, serviceName, currentService, context);

                    logger.debug("服务验证拦截器已应用: {} -> {}",
                               interceptor.getClass().getSimpleName(),
                               currentService != null ? currentService.getClass().getSimpleName() : "null");
                }
            } catch (Exception e) {
                logger.error("应用服务验证拦截器时发生异常: {}", interceptor.getClass().getSimpleName(), e);
                // 继续使用当前服务实例，不因拦截器异常而中断
            }
        }

        return currentService;
    }

    /**
     * 检测类所属的Bundle
     *
     * @param clazz 要检测的类
     * @return Bundle实例，如果无法检测则返回null
     */
    private Bundle detectBundleForClass(Class<?> clazz) {
        if (clazz == null) {
            return null;
        }

        try {
            // 方法1：使用OSGi FrameworkUtil（最准确的方式）
            Bundle bundle = FrameworkUtil.getBundle(clazz);
            if (bundle != null) {
                logger.debug("通过FrameworkUtil检测到Bundle: {} -> {}", clazz.getName(), bundle.getSymbolicName());
                return bundle;
            }
        } catch (Exception e) {
            logger.debug("FrameworkUtil检测Bundle失败: {}", clazz.getName(), e);
        }

        try {
            // 方法2：通过ClassLoader匹配已注册的Bundle
            ClassLoader classLoader = clazz.getClassLoader();
            if (classLoader != null && bundleContext != null) {
                for (Bundle registeredBundle : bundleRegistry.values()) {
                    try {
                        // 尝试通过Bundle加载同一个类，如果成功说明类属于该Bundle
                        Class<?> testClass = registeredBundle.loadClass(clazz.getName());
                        if (testClass == clazz) {
                            logger.debug("通过ClassLoader匹配检测到Bundle: {} -> {}", clazz.getName(), registeredBundle.getSymbolicName());
                            return registeredBundle;
                        }
                    } catch (Exception e) {
                        // 忽略加载失败，继续尝试下一个Bundle
                    }
                }
            }
        } catch (Exception e) {
            logger.debug("ClassLoader匹配检测Bundle失败: {}", clazz.getName(), e);
        }

        // 不使用包名推断，避免不准确的推测导致bug

        logger.debug("无法检测类所属的Bundle: {}", clazz.getName()); 
        return null;
    }



    /**
     * 记录类到Bundle的映射关系
     *
     * @param clazz 类
     * @param bundle Bundle
     */
    private void recordClassToBundleMapping(Class<?> clazz, Bundle bundle) {
        if (clazz != null && bundle != null) {
            classToBundleMap.put(clazz, bundle);
            logger.debug("记录类到Bundle映射: {} -> {}", clazz.getName(), bundle.getSymbolicName());
        }
    }

    /**
     * 记录服务名称到Bundle的映射关系
     *
     * @param serviceName 服务名称
     * @param bundle Bundle
     */
    private void recordServiceNameToBundleMapping(String serviceName, Bundle bundle) {
        if (serviceName != null && bundle != null) {
            serviceNameToBundleMap.put(serviceName, bundle);
            logger.debug("记录服务名称到Bundle映射: {} -> {}", serviceName, bundle.getSymbolicName());
        }
    }

    /**
     * 从服务名称或服务类推断插件ID
     *
     * @param serviceName 服务名称
     * @param serviceClass 服务类
     * @return 插件ID
     */
    private String extractPluginIdFromServiceName(String serviceName, Class<?> serviceClass) {
        // 优先级1：通过服务名称查找Bundle映射
        if (serviceName != null) {
            Bundle bundle = serviceNameToBundleMap.get(serviceName);
            if (bundle != null) {
                String symbolicName = bundle.getSymbolicName();
                logger.debug("通过服务名称映射获取插件ID: {} -> {}", serviceName, symbolicName);
                return symbolicName;
            }
        }

        // 优先级2：通过服务类查找Bundle映射
        if (serviceClass != null) {
            Bundle bundle = classToBundleMap.get(serviceClass);
            if (bundle != null) {
                String symbolicName = bundle.getSymbolicName();
                logger.debug("通过服务类映射获取插件ID: {} -> {}", serviceClass.getName(), symbolicName);
                return symbolicName;
            }
        }

        // 优先级3：尝试动态检测服务类所属的Bundle
        if (serviceClass != null) {
            Bundle bundle = detectBundleForClass(serviceClass);
            if (bundle != null) {
                String symbolicName = bundle.getSymbolicName();
                // 记录映射关系以便下次快速查找
                recordClassToBundleMapping(serviceClass, bundle);
                logger.debug("通过动态检测获取插件ID: {} -> {}", serviceClass.getName(), symbolicName);
                return symbolicName;
            }
        }

        // 无法通过准确的Bundle映射获取插件ID
        logger.warn("无法准确确定插件ID - serviceName={}, serviceClass={}",
                   serviceName, serviceClass != null ? serviceClass.getName() : "null");

        return "unknown";
    }

    // --- 私有辅助方法 ---
    
    private Object resolveService(Class<?> serviceClass, String serviceName) throws Exception {
        logger.debug("开始解析服务: {}, 服务名: {}", serviceClass.getName(), serviceName);

        // 1. 按名称查找
        if (serviceName != null) {
            Object namedService = namedServices.get(serviceName);
            if (namedService != null) {
                logger.debug("通过名称找到服务: {}", namedService.getClass().getName());
                return namedService;
            }
        }

        // 2. 查找单例服务
        Object singleton = singletonServices.get(serviceClass);
        if (singleton != null) {
            logger.debug("找到单例服务: {} -> {}", serviceClass.getName(), singleton.getClass().getName());
            return singleton;
        } else {
            logger.debug("单例服务缓存中未找到: {}, 当前缓存大小: {}", serviceClass.getName(), singletonServices.size());
            // 输出当前缓存中的所有服务类型
            for (Class<?> cachedClass : singletonServices.keySet()) {
                logger.debug("缓存中的服务: {}", cachedClass.getName());
            }
        }
        
        // 3. 查找OSGi服务
        Object osgiService = osgiServices.get(serviceClass);
        if (osgiService != null) {
            return osgiService;
        }
        
        // 4. 查找实现类
        Class<?> implementationClass = serviceImplementations.get(serviceClass);

        // 应用服务解析器
        implementationClass = applyServiceResolvers(serviceClass, null, implementationClass);

        if (implementationClass != null) {
            logger.debug("找到实现类: {} -> {}", serviceClass.getName(), implementationClass.getName());

            // 检查是否应该创建单例
            boolean shouldBeSingleton = hasSingletonAnnotation(implementationClass) || shouldBeSingleton(serviceClass, implementationClass);
            logger.debug("是否应该作为单例: {}, @Singleton注解: {}, 延迟单例: {}, @Service单例scope: {}, 支持@AliasFor: {}",
                        shouldBeSingleton,
                        hasSingletonAnnotation(implementationClass),
                        lazySingletonServices.contains(serviceClass),
                        hasServiceSingletonScope(implementationClass),
                        hasAliasForSupport(implementationClass));

            if (shouldBeSingleton) {
                // 延迟初始化单例
                synchronized (this) {
                    Object existingSingleton = singletonServices.get(serviceClass);
                    if (existingSingleton == null) {
                        logger.debug("开始延迟创建单例服务: {} -> {}", serviceClass.getName(), implementationClass.getName());
                        Object newInstance = createInstance(implementationClass);
                        singletonServices.put(serviceClass, newInstance);
                        logger.info("延迟创建单例服务完成: {} -> {}", serviceClass.getName(), implementationClass.getName());
                        return newInstance;
                    } else {
                        logger.debug("返回已存在的单例: {} -> {}", serviceClass.getName(), existingSingleton.getClass().getName());
                        return existingSingleton;
                    }
                }
            } else {
                logger.debug("创建原型实例: {} -> {}", serviceClass.getName(), implementationClass.getName());
                // 创建原型实例
                return createInstance(implementationClass);
            }
        } else {
            logger.debug("未找到实现类: {}", serviceClass.getName());
        }
        
        // 5. 如果是具体类，直接创建实例
        if (!serviceClass.isInterface() && !java.lang.reflect.Modifier.isAbstract(serviceClass.getModifiers())) {
            return createInstance(serviceClass);
        }

        // 6. 最后尝试从 OSGi 服务总线获取服务（类似 Spring 的 BeanFactoryProvider）
        if (bundleContext != null) {
            Object osgiBusService = getServiceFromOSGiBus(serviceClass);
            if (osgiBusService != null) {
                logger.debug("从 OSGi 服务总线获取服务: {} -> {}", serviceClass.getName(), osgiBusService.getClass().getName());
                // 缓存到 osgiServices 中以提高后续访问性能
                osgiServices.put(serviceClass, osgiBusService);
                return osgiBusService;
            }
        }

        logger.warn("无法解析服务: {}", serviceClass.getName());
        return null;
    }
    
    private Object resolveServiceByName(Class<?> serviceClass, String name) throws Exception {
        if (name == null || name.isEmpty()) {
            return resolveService(serviceClass, null);
        }
        
        // 1. 查找命名服务实例
        String serviceKey = serviceClass.getName() + "#" + name;
        Object namedService = namedServices.get(serviceKey);
        if (namedService != null) {
            return namedService;
        }
        
        // 2. 查找命名实现类
        Map<String, Class<?>> implementations = multipleImplementations.get(serviceClass);
        if (implementations != null) {
            Class<?> implementationClass = implementations.get(name);

            // 应用服务解析器
            implementationClass = applyServiceResolvers(serviceClass, name, implementationClass);

            if (implementationClass != null) {
                return createInstance(implementationClass);
            }
        }
        
        logger.warn("无法解析命名服务: {} (name={})", serviceClass.getName(), name);
        return null;
    }

    /**
     * 基于接口名称解析服务
     * 支持无接口定义架构的核心方法
     *
     * @param interfaceName 接口全限定名称
     * @param serviceName 服务名称（可选）
     * @return 服务实例
     */
    private Object resolveServiceByInterfaceName(String interfaceName, String serviceName) throws Exception {
        if (interfaceName == null || interfaceName.trim().isEmpty()) {
            throw new IllegalArgumentException("接口名称不能为空");
        }

        logger.debug("基于接口名称解析服务: {} (serviceName={})", interfaceName, serviceName);

        // 1. 尝试通过反射加载接口类并使用标准解析（包括拦截器机制）
        try {
            Class<?> interfaceClass = loadInterfaceClass(interfaceName);
            if (interfaceClass != null) {
                logger.debug("成功加载接口类，使用标准解析: {}", interfaceName);
                return serviceName != null ?
                    resolveServiceByName(interfaceClass, serviceName) :
                    resolveService(interfaceClass, null);
            }
        } catch (ClassNotFoundException e) {
            logger.debug("接口类未找到，继续尝试其他方式: {}", interfaceName);
        }

        // 2. 尝试从命名服务映射中查找
        if (serviceName != null) {
            String serviceKey = interfaceName + "#" + serviceName;
            Object namedService = namedServices.get(serviceKey);
            if (namedService != null) {
                logger.debug("从命名服务映射获取服务: {}", serviceKey);
                return namedService;
            }
        }

        // 3. 尝试从简单名称映射中查找
        Object simpleNameService = namedServices.get(interfaceName);
        if (simpleNameService != null) {
            logger.debug("从简单名称映射获取服务: {}", interfaceName);
            return simpleNameService;
        }

        logger.warn("无法解析基于接口名称的服务: {} (serviceName={})", interfaceName, serviceName);
        return null;
    }

    /**
     * 尝试加载接口类
     *
     * @param interfaceName 接口全限定名称
     * @return 接口类，如果加载失败返回null
     */
    private Class<?> loadInterfaceClass(String interfaceName) throws ClassNotFoundException {
        try {
            // 1. 尝试使用当前类加载器
            return Class.forName(interfaceName);
        } catch (ClassNotFoundException e1) {
            // 2. 尝试使用线程上下文类加载器
            try {
                ClassLoader contextClassLoader = Thread.currentThread().getContextClassLoader();
                if (contextClassLoader != null) {
                    return Class.forName(interfaceName, true, contextClassLoader);
                }
            } catch (ClassNotFoundException e2) {
                // 3. 尝试使用OSGi Bundle类加载器
                if (bundleContext != null) {
                    for (Bundle bundle : bundleRegistry.values()) {
                        try {
                            return bundle.loadClass(interfaceName);
                        } catch (ClassNotFoundException e3) {
                            // 继续尝试下一个Bundle
                        }
                    }
                }
            }
            throw e1; // 重新抛出原始异常
        }
    }
    
    private <T> T createNewInstance(Class<T> clazz) throws Exception {
        logger.debug("开始创建新实例: {}", clazz.getName());

        // 查找合适的构造函数
        Constructor<T> constructor = findInjectableConstructor(clazz);

        if (constructor != null) {
            logger.debug("找到可注入构造函数: {}, 参数数量: {}", constructor, constructor.getParameterCount());
            // 使用依赖注入构造函数
            Object[] parameters = resolveConstructorParameters(constructor);
            T instance = constructor.newInstance(parameters);
            logger.debug("成功创建实例: {} -> {}", clazz.getName(), instance.getClass().getName());
            return instance;
        } else {
            logger.debug("未找到可注入构造函数，使用默认构造函数: {}", clazz.getName());
            // 使用默认构造函数
            return clazz.getDeclaredConstructor().newInstance();
        }
    }
    
    @SuppressWarnings("unchecked")
    private <T> Constructor<T> findInjectableConstructor(Class<T> clazz) {
        Constructor<?>[] constructors = clazz.getDeclaredConstructors();
        
        // 查找带有@Inject注解的构造函数
        for (Constructor<?> constructor : constructors) {
            if (hasInjectAnnotation(constructor)) {
                return (Constructor<T>) constructor;
            }
        }
        
        // 如果没有找到@Inject注解的构造函数，返回参数最多的构造函数
        Constructor<?> maxParamConstructor = null;
        int maxParams = -1;
        
        for (Constructor<?> constructor : constructors) {
            if (constructor.getParameterCount() > maxParams) {
                maxParams = constructor.getParameterCount();
                maxParamConstructor = constructor;
            }
        }
        
        return (Constructor<T>) maxParamConstructor;
    }
    
    private Object[] resolveConstructorParameters(Constructor<?> constructor) throws Exception {
        Class<?>[] parameterTypes = constructor.getParameterTypes();
        Object[] parameters = new Object[parameterTypes.length];

        logger.debug("解析构造函数参数，参数数量: {}, 构造函数: {}", parameterTypes.length, constructor);

        for (int i = 0; i < parameterTypes.length; i++) {
            logger.debug("解析参数 [{}]: {}", i, parameterTypes[i].getName());
            Object resolvedService = resolveService(parameterTypes[i], null);
            parameters[i] = resolvedService;
            logger.debug("参数 [{}] 解析结果: {}", i, resolvedService != null ? resolvedService.getClass().getName() : "null");
        }

        return parameters;
    }
    
    private void injectDependencies(Object instance) throws Exception {
        Class<?> clazz = instance.getClass();
        
        // 注入字段
        injectFields(instance, clazz);
        
        // 注入方法
        injectMethods(instance, clazz);
    }
    
    private void injectFields(Object instance, Class<?> clazz) throws Exception {
        Field[] fields = clazz.getDeclaredFields();
        
        for (Field field : fields) {
            if (hasInjectAnnotation(field)) {
                field.setAccessible(true);
                
                String serviceName = getServiceName(field);
                Object service = resolveService(field.getType(), serviceName);
                
                if (service != null) {
                    field.set(instance, service);
                    logger.debug("注入字段: {} -> {}", field.getName(), service.getClass().getName());
                }
            }
        }
        
        // 递归处理父类
        Class<?> superClass = clazz.getSuperclass();
        if (superClass != null && superClass != Object.class) {
            injectFields(instance, superClass);
        }
    }
    
    private void injectMethods(Object instance, Class<?> clazz) throws Exception {
        Method[] methods = clazz.getDeclaredMethods();
        
        for (Method method : methods) {
            if (hasInjectAnnotation(method)) {
                method.setAccessible(true);
                
                Object[] parameters = resolveMethodParameters(method);
                method.invoke(instance, parameters);
                
                logger.debug("调用注入方法: {}", method.getName());
            }
        }
    }
    
    private Object[] resolveMethodParameters(Method method) throws Exception {
        Class<?>[] parameterTypes = method.getParameterTypes();
        Object[] parameters = new Object[parameterTypes.length];
        
        for (int i = 0; i < parameterTypes.length; i++) {
            parameters[i] = resolveService(parameterTypes[i], null);
        }
        
        return parameters;
    }
    
    private void callInitMethods(Object instance) throws Exception {
        Method[] methods = instance.getClass().getDeclaredMethods();
        
        for (Method method : methods) {
            if (hasPostConstructAnnotation(method)) {
                method.setAccessible(true);
                method.invoke(instance);
                
                logger.debug("调用初始化方法: {}", method.getName());
            }
        }
    }
    
    private boolean hasInjectAnnotation(java.lang.reflect.AnnotatedElement element) {
        // 支持JSR-330标准注解
        return element.getAnnotation(Inject.class) != null;
    }

    private boolean hasPostConstructAnnotation(java.lang.reflect.AnnotatedElement element) {
        // 支持JSR-330标准注解
        return element.getAnnotation(PostConstruct.class) != null;
    }

    private boolean hasSingletonAnnotation(Class<?> clazz) {
        // 检查JSR-330 @Singleton注解
        return clazz.getAnnotation(Singleton.class) != null;
    }



    /**
     * 检查类是否有@Service注解且scope为SINGLETON
     * 支持@AliasFor属性别名机制
     */
    private boolean hasServiceSingletonScope(Class<?> clazz) {
        // 1. 直接检查@Service注解
        Service serviceAnnotation = clazz.getAnnotation(Service.class);
        if (serviceAnnotation != null) {
            return serviceAnnotation.scope() == Service.Scope.SINGLETON;
        }

        // 2. 检查组合注解（支持@AliasFor机制）
        for (Annotation annotation : clazz.getAnnotations()) {
            Class<? extends Annotation> annotationType = annotation.annotationType();

            // 检查注解类型是否有@Service元注解
            Service metaService = annotationType.getAnnotation(Service.class);
            if (metaService != null) {
                // 使用@AliasFor机制获取scope值
                Object scopeValue = AliasForUtils.getAliasedAttributeValue(annotation, Service.class, "scope");
                if (scopeValue instanceof Service.Scope) {
                    Service.Scope scope = (Service.Scope) scopeValue;
                    logger.debug("通过@AliasFor获取scope值: {} -> {}", annotationType.getSimpleName(), scope);
                    return scope == Service.Scope.SINGLETON;
                }

                // 使用元注解的默认scope
                logger.debug("使用元注解默认scope值: {} -> {}", annotationType.getSimpleName(), metaService.scope());
                return metaService.scope() == Service.Scope.SINGLETON;
            }
        }

        return false;
    }



    /**
     * 判断服务是否应该作为单例
     */
    private boolean shouldBeSingleton(Class<?> serviceClass, Class<?> implementationClass) {
        // 1. 检查实现类是否有@Singleton注解
        if (hasSingletonAnnotation(implementationClass)) {
            return true;
        }

        // 2. 检查是否通过registerSingletonImplementation注册
        if (lazySingletonServices.contains(serviceClass)) {
            return true;
        }

        // 3. 检查@Service注解中的scope属性
        if (hasServiceSingletonScope(implementationClass)) {
            return true;
        }

        return false;
    }

    /**
     * 检查类是否支持@AliasFor机制
     */
    private boolean hasAliasForSupport(Class<?> clazz) {
        for (Annotation annotation : clazz.getAnnotations()) {
            Class<? extends Annotation> annotationType = annotation.annotationType();

            // 检查是否有@Service元注解
            if (annotationType.getAnnotation(Service.class) != null) {
                // 检查是否有指向Service.scope的@AliasFor
                if (AliasForUtils.hasAliasFor(annotationType, Service.class, "scope")) {
                    return true;
                }
            }
        }
        return false;
    }



    private String getServiceName(Field field) {
        // 优先检查JSR-330 @Named注解
        Named named = field.getAnnotation(Named.class);
        if (named != null) {
            return named.value();
        }

        // 检查自定义@Qualifier注解（如果还需要保留）
        Qualifier qualifier = field.getAnnotation(Qualifier.class);
        return qualifier != null ? qualifier.value() : null;
    }
    
    /**
     * 应用服务解析器
     */
    private Class<?> applyServiceResolvers(Class<?> serviceClass, String serviceName, Class<?> defaultImplementation) {
        for (IServiceResolver resolver : serviceResolvers) {
            if (resolver.shouldResolve(serviceClass)) {
                Class<?> resolvedImplementation = resolver.resolveImplementation(serviceClass, serviceName, defaultImplementation);
                if (resolvedImplementation != null) {
                    logger.debug("服务解析器 {} 选择了实现: {} -> {}",
                               resolver.getClass().getSimpleName(),
                               serviceClass.getName(),
                               resolvedImplementation.getName());
                    return resolvedImplementation;
                }
            }
        }
        return defaultImplementation;
    }

    /**
     * 应用前置拦截器
     */
    private Object applyBeforeInterceptors(Class<?> serviceClass, IInjectionContext context) {
        for (IServiceInterceptor interceptor : interceptors) {
            if (interceptor.shouldIntercept(serviceClass)) {
                Object result = interceptor.beforeCreate(serviceClass, context);
                if (result != null) {
                    logger.debug("拦截器 {} 提供了实例: {}", interceptor.getClass().getSimpleName(), serviceClass.getName());
                    return result;
                }
            }
        }
        return null;
    }
    
    /**
     * 应用后置拦截器
     */
    private Object applyAfterInterceptors(Class<?> serviceClass, Object instance, IInjectionContext context) {
        Object result = instance;

        // 1. 应用传统的服务拦截器
        for (IServiceInterceptor interceptor : interceptors) {
            if (interceptor.shouldIntercept(serviceClass)) {
                result = interceptor.afterCreate(serviceClass, result, context);
            }
        }

        // 2. 应用方法级别的拦截代理（如果需要）
        if (!enhancedInterceptors.isEmpty() && serviceClass.isInterface()) {
            result = createMethodInterceptorProxyUnsafe(serviceClass, result);
        }

        return result;
    }

    /**
     * 创建方法拦截代理
     */
    @SuppressWarnings("unchecked")
    private <T> T createMethodInterceptorProxy(Class<T> serviceClass, T instance) {
        // 过滤出需要方法拦截的拦截器
        List<IEnhancedServiceInterceptor> applicableInterceptors = enhancedInterceptors.stream()
            .filter(interceptor -> interceptor.needsMethodInterception(serviceClass, instance))
            .collect(java.util.stream.Collectors.toList());

        if (applicableInterceptors.isEmpty()) {
            return instance;
        }

        logger.debug("为服务 {} 创建方法拦截代理，拦截器数量: {}",
                   serviceClass.getSimpleName(), applicableInterceptors.size());

        return MethodInterceptorProxy.createProxy(instance, serviceClass, applicableInterceptors);
    }

    /**
     * 创建方法拦截代理（类型不安全版本）
     * 用于处理泛型类型擦除的情况
     */
    @SuppressWarnings("unchecked")
    private Object createMethodInterceptorProxyUnsafe(Class<?> serviceClass, Object instance) {
        // 过滤出需要方法拦截的拦截器
        List<IEnhancedServiceInterceptor> applicableInterceptors = enhancedInterceptors.stream()
            .filter(interceptor -> interceptor.needsMethodInterception(serviceClass, instance))
            .collect(java.util.stream.Collectors.toList());

        if (applicableInterceptors.isEmpty()) {
            return instance;
        }

        logger.debug("为服务 {} 创建方法拦截代理，拦截器数量: {}",
                   serviceClass.getSimpleName(), applicableInterceptors.size());

        return MethodInterceptorProxy.createProxy(instance, (Class<Object>) serviceClass, applicableInterceptors);
    }

    /**
     * 应用前置后置处理器（在依赖注入前）
     */
    private Object applyPostProcessorsBeforeInjection(Object instance, Class<?> serviceClass) {
        Object result = instance;

        for (IPostProcessor processor : postProcessors) {
            if (processor.shouldProcess(serviceClass)) {
                result = processor.postProcessBeforeInjection(result, serviceClass);
                logger.debug("应用前置后置处理器: {} -> {}",
                           processor.getName(), serviceClass.getSimpleName());
            }
        }

        return result;
    }

    /**
     * 应用后置后置处理器（在依赖注入后）
     */
    private Object applyPostProcessorsAfterInjection(Object instance, Class<?> serviceClass) {
        Object result = instance;

        for (IPostProcessor processor : postProcessors) {
            if (processor.shouldProcess(serviceClass)) {
                result = processor.postProcessAfterInjection(result, serviceClass);
                logger.debug("应用后置后置处理器: {} -> {}",
                           processor.getName(), serviceClass.getSimpleName());
            }
        }

        return result;
    }

    /**
     * 触发后置处理器的单例创建完成回调
     */
    private void triggerPostProcessorsAfterSingletonCreation() {
        if (postProcessors.isEmpty()) {
            return;
        }

        logger.debug("触发后置处理器的单例创建完成回调，处理器数量: {}", postProcessors.size());

        for (IPostProcessor processor : postProcessors) {
            try {
                processor.postProcessAfterSingletonCreation(this);
                logger.debug("成功执行后置处理器: {}", processor.getName());
            } catch (Exception e) {
                logger.error("执行后置处理器失败: {}", processor.getName(), e);
            }
        }

        logger.debug("所有后置处理器执行完成");
    }

    /**
     * 扫描指定包下的类
     */
    private List<Class<?>> scanClasses(String packageName) {
        List<Class<?>> classes = new ArrayList<>();
        
        try {
            String packagePath = packageName.replace('.', '/');
            
            if (bundleContext != null) {
                // OSGi环境下的类扫描 - 扫描所有可用Bundle
                Bundle[] allBundles = bundleContext.getBundles();
                for (Bundle bundle : allBundles) {
                    // 支持 RESOLVED、STARTING、ACTIVE 状态的Bundle
                    int state = bundle.getState();
                    if (state == Bundle.RESOLVED || state == Bundle.STARTING || state == Bundle.ACTIVE) {
                        BundleWiring bundleWiring = bundle.adapt(BundleWiring.class);
                        if (bundleWiring != null) {
                            try {
                                // 扫描bundle中的类
                                java.util.Collection<String> resources = bundleWiring.listResources(
                                    packagePath, "*.class",
                                    BundleWiring.LISTRESOURCES_RECURSE
                                );

                                for (String resource : resources) {
                                    String className = resource.replace('/', '.')
                                                              .replace(".class", "");
                                    try {
                                        Class<?> clazz = bundle.loadClass(className);
                                        classes.add(clazz);
                                    } catch (Exception e) {
                                        // 忽略无法加载的类
                                    }
                                }
                            } catch (Exception e) {
                                logger.debug("扫描Bundle失败: {} - {}", bundle.getSymbolicName(), e.getMessage());
                            }
                        }
                    }
                }
            } else {
                // 标准Java环境下的类扫描
                ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
                if (classLoader == null) {
                    classLoader = getClass().getClassLoader();
                }
                
                try {
                    java.util.Enumeration<java.net.URL> resources = classLoader.getResources(packagePath);
                    while (resources.hasMoreElements()) {
                        java.net.URL resource = resources.nextElement();
                        if (resource.getProtocol().equals("file")) {
                            java.io.File directory = new java.io.File(resource.getFile());
                            scanDirectory(directory, packageName, classes);
                        }
                    }
                } catch (Exception e) {
                    logger.error("扫描类路径失败: {}", packageName, e);
                }
            }
        } catch (Exception e) {
            logger.error("类扫描失败: {}", packageName, e);
        }
        
        return classes;
    }
    
    /**
     * 扫描目录中的类文件
     */
    private void scanDirectory(java.io.File directory, String packageName, List<Class<?>> classes) {
        if (!directory.exists() || !directory.isDirectory()) {
            return;
        }
        
        java.io.File[] files = directory.listFiles();
        if (files == null) {
            return;
        }
        
        for (java.io.File file : files) {
            if (file.isDirectory()) {
                scanDirectory(file, packageName + "." + file.getName(), classes);
            } else if (file.getName().endsWith(".class")) {
                String className = packageName + "." + 
                                 file.getName().substring(0, file.getName().length() - 6);
                try {
                    Class<?> clazz = Class.forName(className);
                    classes.add(clazz);
                } catch (Exception e) { 
                    // 忽略无法加载的类
                }
            }
        }
    }

    // ==================== Bundle 跟踪管理 ====================

    /**
     * 初始化包扫描提供者跟踪机制
     */
    private void initializePackageScanProviderTracking() {
        logger.info("初始化包扫描提供者跟踪机制");

        try {
            // 初始化相关数据结构
            bundlePackageMappings.clear();
            scannedBundles.clear();

            logger.debug("包扫描提供者跟踪机制初始化完成");

        } catch (Exception e) {
            logger.error("初始化包扫描提供者跟踪机制失败", e);
        }
    }

    /**
     * 启动包扫描提供者跟踪
     */
    public void startPackageScanProviderTracking() {
        if (bundleContext != null) {
            try {
                // 创建包扫描提供者服务跟踪器
                packageScanProviderTracker = new org.osgi.util.tracker.ServiceTracker<com.fasnote.alm.injection.api.IPackageScanProvider, com.fasnote.alm.injection.api.IPackageScanProvider>(
                    bundleContext,
                    com.fasnote.alm.injection.api.IPackageScanProvider.class,
                    new PackageScanProviderTrackerCustomizer()
                );

                packageScanProviderTracker.open();
                logger.info("包扫描提供者跟踪已启动");

            } catch (Exception e) {
                logger.error("启动包扫描提供者跟踪失败", e);
            }
        } else {
            logger.warn("无法启动包扫描提供者跟踪：bundleContext为空");
        }
    }

    /**
     * 停止包扫描提供者跟踪
     */
    public void stopPackageScanProviderTracking() {
        if (packageScanProviderTracker != null) {
            try {
                packageScanProviderTracker.close();
                packageScanProviderTracker = null;
                logger.info("包扫描提供者跟踪已停止");
            } catch (Exception e) {
                logger.error("停止包扫描提供者跟踪失败", e);
            }
        }

        // 清理相关数据
        bundlePackageMappings.clear();
        scannedBundles.clear();
    }

    /**
     * 包扫描提供者服务跟踪器自定义器
     */
    private class PackageScanProviderTrackerCustomizer implements org.osgi.util.tracker.ServiceTrackerCustomizer<com.fasnote.alm.injection.api.IPackageScanProvider, com.fasnote.alm.injection.api.IPackageScanProvider> {

        @Override
        public com.fasnote.alm.injection.api.IPackageScanProvider addingService(org.osgi.framework.ServiceReference<com.fasnote.alm.injection.api.IPackageScanProvider> reference) {
            com.fasnote.alm.injection.api.IPackageScanProvider provider = bundleContext.getService(reference);
            if (provider != null) {
                addPackageScanProvider(provider, reference.getBundle());
            }
            return provider;
        }

        @Override
        public void modifiedService(org.osgi.framework.ServiceReference<com.fasnote.alm.injection.api.IPackageScanProvider> reference, com.fasnote.alm.injection.api.IPackageScanProvider provider) {
            if (provider != null) {
                removePackageScanProvider(provider, reference.getBundle());
                addPackageScanProvider(provider, reference.getBundle());
            }
        }

        @Override
        public void removedService(org.osgi.framework.ServiceReference<com.fasnote.alm.injection.api.IPackageScanProvider> reference, com.fasnote.alm.injection.api.IPackageScanProvider provider) {
            if (provider != null) {
                removePackageScanProvider(provider, reference.getBundle());
            }
            bundleContext.ungetService(reference);
        }
    }

    /**
     * 添加包扫描提供者
     */
    private void addPackageScanProvider(com.fasnote.alm.injection.api.IPackageScanProvider provider, org.osgi.framework.Bundle bundle) {
        try {
            // 直接调用IPackageScanProvider的方法
            String[] scanPackages = provider.getScanPackages();
            String bundleSymbolicName = bundle.getSymbolicName();

            if (scanPackages != null && scanPackages.length > 0) {
                bundlePackageMappings.put(bundleSymbolicName, scanPackages);
                logger.info("Bundle {} 注册DI扫描包路径: {}", bundleSymbolicName, java.util.Arrays.toString(scanPackages));

                // 触发Bundle扫描
                scanBundleIfReady(bundle, scanPackages);

            } else {
                logger.debug("Bundle {} 的包扫描提供者返回空包路径，该Bundle不参与DI扫描", bundleSymbolicName);
            }

        } catch (Exception e) {
            logger.error("添加包扫描提供者失败: {}", bundle.getSymbolicName(), e);
        }
    }

    /**
     * 移除包扫描提供者
     */
    private void removePackageScanProvider(com.fasnote.alm.injection.api.IPackageScanProvider provider, org.osgi.framework.Bundle bundle) {
        String bundleSymbolicName = bundle.getSymbolicName();
        String[] removedPackages = bundlePackageMappings.remove(bundleSymbolicName);

        if (removedPackages != null) {
            logger.info("Bundle {} 注销DI扫描包路径: {}", bundleSymbolicName, java.util.Arrays.toString(removedPackages));

            // 清理相关的扫描记录
            scannedBundles.remove(bundleSymbolicName);

            // 清理Bundle相关的服务映射
            cleanupBundleMappings(bundle);
        } else {
            logger.debug("Bundle {} 未注册DI扫描包路径，无需移除", bundleSymbolicName);
        }
    }

    /**
     * 如果Bundle准备就绪则进行扫描
     */
    private void scanBundleIfReady(org.osgi.framework.Bundle bundle, String[] scanPackages) {
        if (bundle == null || scanPackages == null || scanPackages.length == 0) {
            return;
        }

        String bundleSymbolicName = bundle.getSymbolicName();

        // 检查Bundle状态
        int state = bundle.getState();
        if (state != org.osgi.framework.Bundle.RESOLVED &&
            state != org.osgi.framework.Bundle.STARTING &&
            state != org.osgi.framework.Bundle.ACTIVE) {
            logger.debug("Bundle {} 状态不适合扫描: {}", bundleSymbolicName, state);
            return;
        }

        logger.info("Bundle {} 就绪，开始扫描服务 (状态: {}, 扫描包: {})",
                   bundleSymbolicName, state, java.util.Arrays.toString(scanPackages));

        try {
            // 注册Bundle到注册表
            registerBundle(bundle);

            // 扫描Bundle中的服务
            scanBundleServices(bundle, scanPackages);

            // 标记为已扫描
            scannedBundles.add(bundleSymbolicName);

        } catch (Exception e) {
            logger.error("扫描Bundle服务失败: {}", bundleSymbolicName, e);
        }
    }

    /**
     * 扫描 Bundle 中的服务
     */
    private void scanBundleServices(Bundle bundle, String[] scanPackages) {
        if (bundle == null) {
            return;
        }

        String bundleSymbolicName = bundle.getSymbolicName();

        // 检查扫描包路径，如果为空则跳过扫描
        if (scanPackages == null || scanPackages.length == 0) {
            logger.debug("Bundle {} 未声明扫描包路径，跳过DI扫描", bundleSymbolicName);
            return;
        }

        logger.info("开始扫描 Bundle 服务: {} (状态: {}, 扫描包: {})",
                   bundleSymbolicName, bundle.getState(), java.util.Arrays.toString(scanPackages));

        try {
            // 记录扫描开始时间
            long startTime = System.currentTimeMillis();

            // 扫描统计
            BundleScanResult scanResult = new BundleScanResult(bundleSymbolicName);

            for (String packageName : scanPackages) {
                try {
                    scanBundlePackage(bundle, packageName, scanResult);
                } catch (Exception e) {
                    logger.debug("扫描 Bundle 包失败: {} - {} - {}", bundleSymbolicName, packageName, e.getMessage());
                    scanResult.addError(packageName, e.getMessage());
                }
            }

            // 记录扫描结果
            long duration = System.currentTimeMillis() - startTime;
            scanResult.setDuration(duration);

            logger.info("Bundle 服务扫描完成: {} - 耗时: {}ms, 模块: {}, 服务: {}, 错误: {}",
                       bundleSymbolicName, duration,
                       scanResult.getModuleCount(),
                       scanResult.getServiceCount(),
                       scanResult.getErrorCount());

        } catch (Exception e) {
            logger.error("扫描 Bundle 服务失败: {}", bundleSymbolicName, e);
        }
    }

    /**
     * 扫描 Bundle 中指定包的类
     */
    private void scanBundlePackage(Bundle bundle, String packageName, BundleScanResult scanResult) {
        try {
            String packagePath = packageName.replace('.', '/');

            org.osgi.framework.wiring.BundleWiring bundleWiring = bundle.adapt(org.osgi.framework.wiring.BundleWiring.class);
            if (bundleWiring == null) {
                logger.debug("Bundle 无法获取 BundleWiring: {}", bundle.getSymbolicName());
                return;
            }

            // 扫描 Bundle 中的类
            java.util.Collection<String> resources = bundleWiring.listResources(
                packagePath, "*.class",
                org.osgi.framework.wiring.BundleWiring.LISTRESOURCES_RECURSE
            );

            if (resources.isEmpty()) {
                logger.debug("Bundle {} 的包 {} 中未找到类文件", bundle.getSymbolicName(), packageName);
                return;
            }

            logger.debug("Bundle {} 的包 {} 中发现 {} 个类文件", bundle.getSymbolicName(), packageName, resources.size());

            for (String resource : resources) {
                String className = resource.replace('/', '.').replace(".class", "");
                try {
                    // 尝试加载类，处理Bundle类可见性问题
                    Class<?> clazz = loadClassSafely(bundle, className);
                    if (clazz == null) {
                        continue; // 跳过无法加载的类
                    }

                    // 检查是否为 IModule 实现
                    if (IModule.class.isAssignableFrom(clazz) &&
                        !clazz.isInterface() &&
                        !java.lang.reflect.Modifier.isAbstract(clazz.getModifiers())) {

                        logger.info("发现 IModule 实现: {} (Bundle: {})", className, bundle.getSymbolicName());
                        scanResult.incrementModuleCount();

                        try {
                            // 尝试创建模块实例并安装
                            IModule module = createModuleInstance(clazz);
                            if (module != null) {
                                installModule(module);
                                logger.debug("成功安装模块: {}", className);
                            } else {
                                logger.warn("无法创建模块实例: {}", className);
                                scanResult.addError(className, "模块实例化失败：无法创建实例");
                            }
                        } catch (Exception e) {
                            logger.error("创建模块实例失败: {}", className, e);
                            scanResult.addError(className, "模块实例化失败: " + e.getMessage());
                        }
                    }

                    // 检查是否有基础 DI 注解
                    if (hasBasicDIAnnotation(clazz)) {
                        logger.debug("发现服务类: {} (Bundle: {})", className, bundle.getSymbolicName());
                        scanResult.incrementServiceCount();

                        // 尝试自动注册服务
                        tryAutoRegisterService(clazz, bundle);
                    }

                } catch (Exception e) {
                    // 记录类加载失败的详细信息
                    logger.debug("扫描类时发生异常: {} (Bundle: {}) - {}", className, bundle.getSymbolicName(), e.getMessage());
                    scanResult.addError(className, "类扫描失败: " + e.getMessage());
                }
            }

        } catch (Exception e) {
            logger.error("扫描 Bundle 包失败: {} - {}", bundle.getSymbolicName(), packageName, e);
            scanResult.addError(packageName, "包扫描失败: " + e.getMessage());
        }
    }

    /**
     * 检查类是否有基础 DI 注解
     */
    private boolean hasBasicDIAnnotation(Class<?> clazz) {
        try {

            // 检查标准JSR-330注解
            if (clazz.isAnnotationPresent(javax.inject.Named.class)) {
                logger.debug("发现 @Named 注解: {}", clazz.getName());
                return true;
            }

            if (clazz.isAnnotationPresent(javax.inject.Singleton.class)) {
                logger.debug("发现 @Singleton 注解: {}", clazz.getName());
                return true;
            }

            // 检查是否有@Service注解或包含@Service的组合注解
            if (hasServiceAnnotationOrComposite(clazz)) {
                return true;
            }

        } catch (Exception e) {
            logger.error("检查基础 DI 注解失败: {} - {}", clazz.getName(), e.getMessage(), e);
        }

        // logger.debug("类 {} 没有发现DI注解", clazz.getName());
        return false;
    }

    /**
     * 检查类是否有@Service注解或包含@Service的组合注解
     */
    private boolean hasServiceAnnotationOrComposite(Class<?> clazz) {
        for (Annotation annotation : clazz.getAnnotations()) {
            if (isServiceRelatedAnnotation(annotation.annotationType())) {
                logger.debug("发现服务相关注解: {} 在类 {}",
                            annotation.annotationType().getSimpleName(), clazz.getName());
                return true;
            }
        }
        return false;
    }

    /**
     * 检查注解类型是否为服务相关注解
     */
    private boolean isServiceRelatedAnnotation(Class<? extends Annotation> annotationType) {
        // 直接检查是否为@Service注解
        if (Service.class.equals(annotationType)) {
            return true;
        }

        // 检查是否为包含@Service元注解的组合注解
        return hasServiceMetaAnnotation(annotationType);
    }

    /**
     * 检查注解类型是否被@Service注解标记（组合注解检查）
     * 优化版本：避免硬编码字符串比较，提高性能
     */
    private boolean hasServiceMetaAnnotation(Class<? extends Annotation> annotationType) {
        try {
            // 避免检查Java标准注解，提高性能
            String packageName = annotationType.getPackage().getName();
            if (packageName.startsWith("java.") || packageName.startsWith("javax.")) {
                return false;
            }
            Annotation[] metaAnnotations = annotationType.getAnnotations();

            for (Annotation metaAnnotation : metaAnnotations) {
                // 直接比较注解类型，避免字符串比较
                if (Service.class.equals(metaAnnotation.annotationType())) {
                    logger.debug("在注解 {} 中发现 @Service 元注解", annotationType.getName());
                    return true;
                }
            }
        } catch (Exception e) {
            logger.debug("检查组合注解失败: {} - {}", annotationType.getName(), e.getMessage());
        }
        logger.debug("注解 {} 不包含 @Service 元注解", annotationType.getName());
        return false;
    }

    /**
     * 尝试自动注册服务（支持多实现）
     */
    private void tryAutoRegisterService(Class<?> serviceClass, Bundle bundle) {
        try {
            // 检查是否为具体实现类
            if (serviceClass.isInterface() ||
                java.lang.reflect.Modifier.isAbstract(serviceClass.getModifiers())) {
                logger.debug("跳过接口或抽象类: {}", serviceClass.getName());
                return;
            }

            // 查找服务接口
            Class<?>[] interfaces = serviceClass.getInterfaces();
            if (interfaces.length > 0) {
                // 使用第一个接口作为服务接口
                Class<?> serviceInterface = interfaces[0];

                logger.info("自动注册服务实现: {} -> {} (Bundle: {})",
                           serviceInterface.getName(), serviceClass.getName(), bundle.getSymbolicName());

                // 添加到多实现列表
                addServiceImplementation(serviceInterface, serviceClass);

                // 记录Bundle映射
                recordClassToBundleMapping(serviceClass, bundle);
                recordClassToBundleMapping(serviceInterface, bundle);

            } else {
                logger.debug("类没有实现接口，跳过自动注册: {}", serviceClass.getName());
            }

        } catch (Exception e) {
            logger.warn("自动注册服务失败: {} - {}", serviceClass.getName(), e.getMessage());
        }
    }

    /**
     * 添加服务实现到多实现列表
     */
    private void addServiceImplementation(Class<?> serviceInterface, Class<?> implementationClass) {
        // 添加到多实现映射
        multipleImplementations.computeIfAbsent(serviceInterface, k -> new ConcurrentHashMap<>())
                              .put(implementationClass.getSimpleName(), implementationClass);

        // 添加到优先级列表
        implementationsByPriority.computeIfAbsent(serviceInterface, k -> new ArrayList<>())
                                 .add(implementationClass);

        // 如果是第一个实现，设为默认实现（向后兼容）
        if (!serviceImplementations.containsKey(serviceInterface)) {
            serviceImplementations.put(serviceInterface, implementationClass);
            logger.debug("设置默认服务实现: {} -> {}", serviceInterface.getName(), implementationClass.getName());
        }

        logger.debug("添加服务实现: {} -> {} (总数: {})",
                   serviceInterface.getName(), implementationClass.getName(),
                   multipleImplementations.get(serviceInterface).size());
    }

    /**
     * 获取服务接口的所有实现类
     */
    public List<Class<?>> getServiceImplementations(Class<?> serviceInterface) {
        Map<String, Class<?>> implementations = multipleImplementations.get(serviceInterface);
        if (implementations == null || implementations.isEmpty()) {
            return new ArrayList<>();
        }
        return new ArrayList<>(implementations.values());
    }



    /**
     * 清理 Bundle 相关的映射
     */
    private void cleanupBundleMappings(Bundle bundle) {
        try {
            // 清理类到 Bundle 的映射
            classToBundleMap.entrySet().removeIf(entry -> bundle.equals(entry.getValue()));

            // 清理服务名称到 Bundle 的映射
            serviceNameToBundleMap.entrySet().removeIf(entry -> bundle.equals(entry.getValue()));

            logger.debug("清理 Bundle 映射完成: {}", bundle.getSymbolicName());

        } catch (Exception e) {
            logger.error("清理 Bundle 映射失败: {}", bundle.getSymbolicName(), e);
        }
    }

    /**
     * Bundle 扫描结果统计
     */
    private static class BundleScanResult {
        private final String bundleSymbolicName;
        private int moduleCount = 0;
        private int serviceCount = 0;
        private int errorCount = 0;
        private long duration = 0;
        private final List<String> errors = new ArrayList<>();

        public BundleScanResult(String bundleSymbolicName) {
            this.bundleSymbolicName = bundleSymbolicName;
        }

        public void incrementModuleCount() {
            moduleCount++;
        }

        public void incrementServiceCount() {
            serviceCount++;
        }

        public void addError(String context, String error) {
            errors.add(context + ": " + error);
            errorCount++;
        }

        public int getModuleCount() { return moduleCount; }
        public int getServiceCount() { return serviceCount; }
        public int getErrorCount() { return errorCount; }
        public long getDuration() { return duration; }
        public void setDuration(long duration) { this.duration = duration; }
        public String getBundleSymbolicName() { return bundleSymbolicName; }
        public List<String> getErrors() { return new ArrayList<>(errors); }
    }

    /**
     * 安全地加载类，处理Bundle类可见性问题
     */
    private Class<?> loadClassSafely(Bundle bundle, String className) {
        try {
            // 首先尝试使用Bundle的类加载器
            Class<?> clazz = bundle.loadClass(className);
            logger.debug("成功加载类: {} (Bundle: {}, ClassLoader: {})",
                        className, bundle.getSymbolicName(), clazz.getClassLoader().getClass().getName());
            return clazz;

        } catch (ClassNotFoundException e) {
            logger.debug("Bundle {} 无法加载类 {}: {}", bundle.getSymbolicName(), className, e.getMessage());

            // 检查是否是类可见性问题
            if (e.getMessage() != null && e.getMessage().contains("cannot be found by")) {
                logger.debug("类可见性问题，跳过类: {} (Bundle: {})", className, bundle.getSymbolicName());
            }

            return null;

        } catch (NoClassDefFoundError e) {
            logger.debug("类定义未找到，跳过类: {} (Bundle: {}) - {}", className, bundle.getSymbolicName(), e.getMessage());
            return null;

        } catch (LinkageError e) {
            logger.debug("类链接错误，跳过类: {} (Bundle: {}) - {}", className, bundle.getSymbolicName(), e.getMessage());
            return null;

        } catch (Exception e) {
            logger.debug("加载类时发生未知异常: {} (Bundle: {}) - {}", className, bundle.getSymbolicName(), e.getMessage());
            return null;
        }
    }

    /**
     * 安装模块依赖
     *
     * @param module 要安装的模块
     */
    private void installModuleDependencies(IModule module) {
        List<Class<? extends IModule>> dependencies = module.getDependencies();
        if (dependencies == null || dependencies.isEmpty()) {
            return;
        }

        logger.debug("模块 {} 声明了 {} 个依赖", module.getName(), dependencies.size());

        for (Class<? extends IModule> dependencyClass : dependencies) {
            // 检查依赖模块是否已安装
            boolean isInstalled = installedModules.stream()
                .anyMatch(m -> m.getClass().equals(dependencyClass));

            if (!isInstalled) {
                logger.debug("安装依赖模块: {}", dependencyClass.getName());
                try {
                    // 创建依赖模块实例
                    IModule dependencyModule = createModuleInstance(dependencyClass);
                    if (dependencyModule != null) {
                        // 递归安装依赖模块（这会处理传递依赖）
                        installModule(dependencyModule);
                    } else {
                        throw new IllegalStateException("无法创建依赖模块实例: " + dependencyClass.getName());
                    }
                } catch (Exception e) {
                    throw new IllegalStateException(
                        String.format("安装模块 %s 的依赖 %s 失败: %s",
                                    module.getName(), dependencyClass.getName(), e.getMessage()), e);
                }
            } else {
                logger.debug("依赖模块 {} 已安装", dependencyClass.getName());
            }
        }
    }

    /**
     * 创建模块实例，强制要求无参构造函数
     *
     * 模块注册的设计原则：
     * 1. 模块应该是配置单元，不应该依赖外部状态
     * 2. 模块的依赖应该通过 configure() 方法中的依赖查找获取
     * 3. 这样可以确保模块的可重用性和测试性
     */
    private IModule createModuleInstance(Class<?> moduleClass) {
        try {
            // 检查是否有无参构造函数
            java.lang.reflect.Constructor<?> defaultConstructor;
            try {
                defaultConstructor = moduleClass.getDeclaredConstructor();
            } catch (NoSuchMethodException e) {
                // 检查是否只有有参构造函数
                java.lang.reflect.Constructor<?>[] constructors = moduleClass.getDeclaredConstructors();
                if (constructors.length > 0) {
                    StringBuilder paramInfo = new StringBuilder();
                    for (java.lang.reflect.Constructor<?> constructor : constructors) {
                        if (paramInfo.length() > 0) {
                            paramInfo.append(", ");
                        }
                        paramInfo.append("(").append(java.util.Arrays.toString(constructor.getParameterTypes())).append(")");
                    }

                    throw new IllegalArgumentException(
                        String.format("模块类 %s 必须提供无参构造函数。" +
                                    "当前只找到有参构造函数: %s。" +
                                    "模块注册的设计原则要求模块使用无参构造函数，" +
                                    "依赖应该通过 configure() 方法中的依赖查找获取，" +
                                    "而不是通过构造函数注入。这样可以确保模块的可重用性和一致性。",
                                    moduleClass.getName(), paramInfo.toString()));
                } else {
                    throw new IllegalArgumentException(
                        String.format("模块类 %s 没有找到任何构造函数", moduleClass.getName()));
                }
            }

            // 使用无参构造函数创建实例
            defaultConstructor.setAccessible(true);
            IModule module = (IModule) defaultConstructor.newInstance();
            logger.debug("成功使用无参构造函数创建模块: {}", moduleClass.getName());
            return module;

        } catch (IllegalArgumentException e) {
            // 重新抛出我们的自定义异常
            throw e;
        } catch (Exception e) {
            logger.error("创建模块实例时发生异常: {}", moduleClass.getName(), e);
            throw new RuntimeException("创建模块实例失败: " + moduleClass.getName(), e);
        }
    }

    /**
     * 自动注册 OSGi 服务到依赖注入容器
     * 在 OSGi 构造函数中调用，将常用的 OSGi 服务注册为可注入的服务
     */
    private void registerOSGiServices() {
        if (bundleContext == null) {
            logger.debug("BundleContext 为空，跳过 OSGi 服务注册");
            return;
        }

        logger.info("开始注册 OSGi 服务到依赖注入容器");

        // 1. 注册 BundleContext 服务提供者
        registerProvider(BundleContext.class, new IServiceProvider<BundleContext>() {
            @Override
            public BundleContext provide(IInjectionContext context) throws Exception {
                if (bundleContext == null) {
                    throw new RuntimeException("BundleContext 不可用");
                }
                logger.debug("提供 BundleContext 实例: {}", bundleContext.getBundle().getSymbolicName());
                return bundleContext;
            }
        });

        logger.info("OSGi 服务注册完成，已注册 {} 个 OSGi 服务提供者", 1);
    }

    /**
     * 从 OSGi 服务总线获取服务
     * 类似于 Spring 的 BeanFactoryProvider，当 DI 容器找不到服务时，自动从 OSGi 服务总线获取
     *
     * @param serviceClass 服务类型
     * @return 服务实例，如果未找到返回 null
     */
    private <T> T getServiceFromOSGiBus(Class<T> serviceClass) {
        if (bundleContext == null || serviceClass == null) {
            return null;
        }

        try {
            // 获取服务引用
            org.osgi.framework.ServiceReference<T> serviceRef = bundleContext.getServiceReference(serviceClass);
            if (serviceRef != null) {
                // 获取服务实例
                T service = bundleContext.getService(serviceRef);
                if (service != null) {
                    logger.debug("成功从 OSGi 服务总线获取服务: {}", serviceClass.getName());
                    return service;
                }
            }
        } catch (Exception e) {
            logger.debug("从 OSGi 服务总线获取服务失败: {} - {}", serviceClass.getName(), e.getMessage());
        }

        return null;
    }

}